# AWS Image Builder Component: Install BGInfo
# This component installs and configures BGInfo to display system information on desktop background
# BGInfo is a Microsoft Sysinternals utility that automatically displays relevant information about a Windows computer

name: win-server-bginfo
description: Install and configure BGInfo to display system information on desktop background
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: CheckExistingBGInfo
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Checking for existing BGInfo installation..."

                # Check if BGInfo is already installed
                $bgInfoPath = "${env:ProgramFiles}\BGInfo\Bginfo.exe"
                $bgInfoPath64 = "${env:ProgramFiles}\BGInfo\Bginfo64.exe"

                if ((Test-Path $bgInfoPath) -or (Test-Path $bgInfoPath64)) {
                    Write-Host "BGInfo is already installed"
                    if (Test-Path $bgInfoPath) {
                        Write-Host "Found BGInfo at: $bgInfoPath"
                    }
                    if (Test-Path $bgInfoPath64) {
                        Write-Host "Found BGInfo64 at: $bgInfoPath64"
                    }
                    exit 0
                } else {
                    Write-Host "BGInfo is not installed. Proceeding with installation..."
                }

      - name: DownloadBGInfo
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Downloading BGInfo from Microsoft Sysinternals..."

                # Create BGInfo directory
                $bgInfoDir = "${env:ProgramFiles}\BGInfo"
                if (!(Test-Path $bgInfoDir)) {
                    New-Item -ItemType Directory -Path $bgInfoDir -Force
                    Write-Host "Created BGInfo directory: $bgInfoDir"
                }

                # Create temp directory
                $tempDir = "C:\DesktopInfo\bginfo"
                if (!(Test-Path $tempDir)) {
                    New-Item -ItemType Directory -Path $tempDir -Force
                    Write-Host "Created temp directory: $tempDir"
                }

                # Download BGInfo from Microsoft
                $bgInfoUrl = "https://download.sysinternals.com/files/BGInfo.zip"
                $zipPath = "$tempDir\BGInfo.zip"

                try {
                    Write-Host "Downloading BGInfo from: $bgInfoUrl"
                    Invoke-WebRequest -Uri $bgInfoUrl -OutFile $zipPath -UseBasicParsing
                    Write-Host "Downloaded BGInfo to: $zipPath"
                } catch {
                    Write-Error "Failed to download BGInfo: $($_.Exception.Message)"
                    exit 1
                }

                # Verify download
                if (Test-Path $zipPath) {
                    $fileSize = (Get-Item $zipPath).Length
                    Write-Host "Downloaded file size: $([math]::Round($fileSize/1KB, 2)) KB"
                    
                    if ($fileSize -lt 10KB) {
                        Write-Error "Downloaded file appears to be too small. Download may have failed."
                        exit 1
                    }
                } else {
                    Write-Error "BGInfo zip file not found after download"
                    exit 1
                }

      - name: ExtractAndInstallBGInfo
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Extracting and installing BGInfo..."

                $tempDir = "C:\DesktopInfo\bginfo"
                $zipPath = "$tempDir\BGInfo.zip"
                $bgInfoDir = "${env:ProgramFiles}\BGInfo"

                # Extract BGInfo
                try {
                    Write-Host "Extracting BGInfo archive..."
                    Expand-Archive -Path $zipPath -DestinationPath $tempDir -Force
                    Write-Host "BGInfo extracted successfully"
                } catch {
                    Write-Error "Failed to extract BGInfo: $($_.Exception.Message)"
                    exit 1
                }

                # Copy BGInfo files to program files
                try {
                    $sourceFiles = Get-ChildItem -Path $tempDir -Filter "*.exe"
                    foreach ($file in $sourceFiles) {
                        $destPath = Join-Path $bgInfoDir $file.Name
                        Copy-Item -Path $file.FullName -Destination $destPath -Force
                        Write-Host "Copied $($file.Name) to $destPath"
                    }
                    
                    # Also copy any other files (like EULA, etc.)
                    $otherFiles = Get-ChildItem -Path $tempDir -Exclude "*.zip"
                    foreach ($file in $otherFiles) {
                        if ($file.PSIsContainer -eq $false -and $file.Extension -ne ".exe") {
                            $destPath = Join-Path $bgInfoDir $file.Name
                            Copy-Item -Path $file.FullName -Destination $destPath -Force
                            Write-Host "Copied $($file.Name) to $destPath"
                        }
                    }
                } catch {
                    Write-Error "Failed to copy BGInfo files: $($_.Exception.Message)"
                    exit 1
                }

      - name: CreateBGInfoConfigurations
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Creating BGInfo configurations for DEV, PPE, and PROD environments..."

                $bgInfoDir = "${env:ProgramFiles}\BGInfo"

                # Get environment from parameter or default to DEV
                $environment = $env:BGINFO_ENVIRONMENT
                if (-not $environment) {
                    $environment = "DEV"
                    Write-Host "No BGINFO_ENVIRONMENT specified, defaulting to DEV"
                }
                Write-Host "Creating BGInfo configuration for environment: $environment"

                # Define environment-specific background colors
                $envColors = @{
                    "DEV" = @{
                        Red = 0; Green = 128; Blue = 0
                        Hue = 80; Sat = 240; Lum = 60
                        Name = "Development"
                    }
                    "PPE" = @{
                        Red = 228; Green = 199; Blue = 27
                        Hue = 34; Sat = 189; Lum = 120
                        Name = "Pre-Production"
                    }
                    "PROD" = @{
                        Red = 255; Green = 0; Blue = 0
                        Hue = 0; Sat = 240; Lum = 120
                        Name = "Production"
                    }
                }

                # Validate environment
                if (-not $envColors.ContainsKey($environment.ToUpper())) {
                    Write-Error "Invalid environment: $environment. Valid options are: DEV, PPE, PROD"
                    exit 1
                }

                $envConfig = $envColors[$environment.ToUpper()]
                Write-Host "Using background color - Red: $($envConfig.Red), Green: $($envConfig.Green), Blue: $($envConfig.Blue)"

                # Create environment-specific configuration
                $configPath = "$bgInfoDir\bginfo-$($environment.ToLower()).bgi"

                # Build the configuration content with environment-specific values
                $envName = $envConfig.Name
                $envUpper = $environment.ToUpper()
                $desktopColor = $envConfig.Red + ($envConfig.Green * 256) + ($envConfig.Blue * 65536)

                # Create BGInfo configuration content
                $configLines = @(
                    "[BGInfo]"
                    "RTF={\rtf1\ansi\deff0{\fonttbl{\f0\fnil\fcharset0 Arial;}}{\colortbl ;\red255\green255\blue255;\red0\green128\blue0;\red255\green0\blue0;}"
                    "\viewkind4\uc1\pard\cf2\ul\b\f0\fs28 <Computer Name>\ulnone\b0\fs20  - <Description>\par"
                    "\par"
                    "\cf1\b Environment:\b0\tab $envName ($envUpper)\par"
                    "\par"
                    "\b User:\b0\tab <Logon Domain>\\<User Name>\par"
                    "\b Domain:\b0\tab <Machine Domain>\par"
                    "\par"
                    "\b OS Version:\b0\tab <OS Version>\par"
                    "\b Service Pack:\b0\tab <Service Pack>\par"
                    "\b Boot Time:\b0\tab <Boot Time>\par"
                    "\par"
                    "\b CPU:\b0\tab <CPU>\par"
                    "\b Memory:\b0\tab <Memory>\par"
                    "\par"
                    "\b Volumes:\b0\tab <Volumes>\par"
                    "\b Free Space:\b0\tab <Free Space>\par"
                    "\par"
                    "\b IP Address:\b0\tab <IP Address>\par"
                    "\b Subnet Mask:\b0\tab <Subnet Mask>\par"
                    "\b Default Gateway:\b0\tab <Default Gateway>\par"
                    "\b DHCP Server:\b0\tab <DHCP Server>\par"
                    "\b DNS Server:\b0\tab <DNS Server>\par"
                    "\par"
                    "\b Logon Server:\b0\tab <Logon Server>\par"
                    "}"
                    "Position=0"
                    "TextWidth2=400"
                    "TextHeight2=300"
                    "LimitTextWidth=0"
                    "BalloonTip=0"
                    "WallpaperPos=0"
                    "OpaqueTextBox=1"
                    "TransparentText=0"
                    "RTF2="
                    "Database=0"
                    "DatabaseMRU="
                    "WMI=0"
                    "DesktopColor=$desktopColor"
                )

                $configContent = $configLines -join "`r`n"

                try {
                    Set-Content -Path $configPath -Value $configContent -Encoding ASCII
                    Write-Host "Created BGInfo configuration at: $configPath"
                    Write-Host "Background color set to RGB($($envConfig.Red), $($envConfig.Green), $($envConfig.Blue))"
                } catch {
                    Write-Error "Failed to create BGInfo configuration: $($_.Exception.Message)"
                    exit 1
                }

                # Also create a default configuration pointing to the environment-specific one
                $defaultConfigPath = "$bgInfoDir\bginfo.bgi"
                try {
                    Copy-Item -Path $configPath -Destination $defaultConfigPath -Force
                    Write-Host "Created default BGInfo configuration at: $defaultConfigPath"
                } catch {
                    Write-Warning "Could not create default configuration: $($_.Exception.Message)"
                }

      - name: ConfigureBGInfoAutostart
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring BGInfo to start automatically..."

                $bgInfoDir = "${env:ProgramFiles}\BGInfo"
                $bgInfoExe = "$bgInfoDir\Bginfo64.exe"

                # Get environment for config file selection
                $environment = $env:BGINFO_ENVIRONMENT
                if (-not $environment) {
                    $environment = "DEV"
                }

                # Use environment-specific config file
                $configPath = "$bgInfoDir\bginfo-$($environment.ToLower()).bgi"

                # Fallback to default config if environment-specific doesn't exist
                if (!(Test-Path $configPath)) {
                    $configPath = "$bgInfoDir\bginfo.bgi"
                    Write-Warning "Environment-specific config not found, using default: $configPath"
                } else {
                    Write-Host "Using environment-specific config: $configPath"
                }

                # Use 64-bit version if available, otherwise fall back to 32-bit
                if (!(Test-Path $bgInfoExe)) {
                    $bgInfoExe = "$bgInfoDir\Bginfo.exe"
                }

                if (!(Test-Path $bgInfoExe)) {
                    Write-Error "BGInfo executable not found"
                    exit 1
                }

                # Create registry entry for all users to run BGInfo at logon
                $regPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
                $regName = "BGInfo"
                $regValue = "`"$bgInfoExe`" `"$configPath`" /timer:0 /nolicprompt /silent"

                try {
                    Set-ItemProperty -Path $regPath -Name $regName -Value $regValue -Force
                    Write-Host "Added BGInfo to startup registry: $regValue"
                } catch {
                    Write-Error "Failed to add BGInfo to startup registry: $($_.Exception.Message)"
                    exit 1
                }

                # Also create a scheduled task for system startup (alternative method)
                try {
                    $taskName = "BGInfo System Startup"
                    $taskDescription = "Display system information on desktop background"
                    $taskAction = New-ScheduledTaskAction -Execute $bgInfoExe -Argument "`"$configPath`" /timer:0 /nolicprompt /silent"
                    $taskTrigger = New-ScheduledTaskTrigger -AtStartup
                    $taskSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
                    $taskPrincipal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
                    
                    Register-ScheduledTask -TaskName $taskName -Description $taskDescription -Action $taskAction -Trigger $taskTrigger -Settings $taskSettings -Principal $taskPrincipal -Force
                    Write-Host "Created scheduled task: $taskName"
                } catch {
                    Write-Warning "Could not create scheduled task (registry method will still work): $($_.Exception.Message)"
                }

      - name: TestBGInfoExecution
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Testing BGInfo execution..."

                $bgInfoDir = "${env:ProgramFiles}\BGInfo"
                $bgInfoExe = "$bgInfoDir\Bginfo64.exe"
                $configPath = "$bgInfoDir\bginfo.bgi"

                # Use 64-bit version if available, otherwise fall back to 32-bit
                if (!(Test-Path $bgInfoExe)) {
                    $bgInfoExe = "$bgInfoDir\Bginfo.exe"
                }

                try {
                    # Test BGInfo execution (this will set the background immediately)
                    Write-Host "Running BGInfo test execution..."
                    $process = Start-Process -FilePath $bgInfoExe -ArgumentList "`"$configPath`" /timer:0 /nolicprompt /silent" -Wait -PassThru -NoNewWindow
                    
                    if ($process.ExitCode -eq 0) {
                        Write-Host "BGInfo executed successfully"
                    } else {
                        Write-Warning "BGInfo execution returned exit code: $($process.ExitCode)"
                    }
                } catch {
                    Write-Warning "Could not test BGInfo execution: $($_.Exception.Message)"
                }

      - name: CleanupTempFiles
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Cleaning up temporary files..."

                $tempDir = "C:\DesktopInfo\bginfo"
                if (Test-Path $tempDir) {
                    try {
                        Remove-Item -Path $tempDir -Recurse -Force
                        Write-Host "Cleaned up temp directory: $tempDir"
                    } catch {
                        Write-Warning "Could not clean up temp directory: $($_.Exception.Message)"
                    }
                }

  - name: validate
    steps:
      - name: ValidateBGInfoInstallation
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating BGInfo installation..."

                $bgInfoDir = "${env:ProgramFiles}\BGInfo"
                $bgInfoExe = "$bgInfoDir\Bginfo64.exe"
                $configPath = "$bgInfoDir\bginfo.bgi"

                # Check for BGInfo executable
                if (!(Test-Path $bgInfoExe)) {
                    $bgInfoExe = "$bgInfoDir\Bginfo.exe"
                }

                if (Test-Path $bgInfoExe) {
                    Write-Host "✓ BGInfo executable found at: $bgInfoExe"
                    
                    # Get version info
                    try {
                        $version = (Get-ItemProperty $bgInfoExe).VersionInfo.FileVersion
                        Write-Host "✓ BGInfo version: $version"
                    } catch {
                        Write-Host "Could not retrieve version information"
                    }
                } else {
                    Write-Error "BGInfo executable not found"
                    exit 1
                }

                # Check for configuration file
                if (Test-Path $configPath) {
                    Write-Host "✓ BGInfo configuration found at: $configPath"
                } else {
                    Write-Warning "BGInfo configuration file not found"
                }

                # Check registry entry
                $regPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
                $regName = "BGInfo"

                try {
                    $regValue = Get-ItemProperty -Path $regPath -Name $regName -ErrorAction SilentlyContinue
                    if ($regValue) {
                        Write-Host "✓ BGInfo startup registry entry found"
                    } else {
                        Write-Warning "BGInfo startup registry entry not found"
                    }
                } catch {
                    Write-Warning "Could not check BGInfo startup registry entry"
                }

                Write-Host "BGInfo installation validation completed successfully"
