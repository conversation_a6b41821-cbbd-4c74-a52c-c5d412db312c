schemaVersion: '0.3'
description: |-
  This document will join or unjoin an EC2 Windows instance to an Active Directory domain.
assumeRole: '{{AutomationAssumeRole}}'
parameters:
  AutomationAssumeRole:
    default: ''
    description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf.
    type: String
  InstanceId:
    description: (Required) The Instance running Windows Server.
    type: String
  DomainJoinActivity:
    allowedValues:
    - Join
    - Unjoin
    - ''
    default: ''
    description: '(Required) Select which AD domain activity to perform, join an AD domain or unjoin an AD domain.'
    type: String
  ConfigS3Bucket:
    description: (Optional) S3 bucket containing the domain join configuration JSON files.
    type: String
    default: ''
  BUPrefix:
    description: (Optional) Business Unit prefix for config file selection (e.g., STM, SLM).
    type: String
    default: ''
  Environment:
    description: (Optional) Environment for config file selection (e.g., DEV, PRD).
    type: String
    default: ''
  ServerType:
    description: (Optional) Server type for OU selection from serverOUs config (e.g., MSSQL-2022, Shared-2022).
    type: String
    default: ''
mainSteps:
- name: assertInstanceIsWindows
  action: 'aws:assertAwsResourceProperty'
  description: ''
  inputs:
    Service: ec2
    PropertySelector: '$.Reservations[0].Instances[0].Platform'
    Api: DescribeInstances
    DesiredValues:
    - windows
    InstanceIds:
    - '{{InstanceId}}'
  timeoutSeconds: 10
  nextStep: chooseDomainJoinActivity
- name: chooseDomainJoinActivity
  action: aws:branch
  timeoutSeconds: 60
  description: Determine the appropriate AD domain activity, join or unjoin.
  inputs:
    Choices:
    - NextStep: joinDomain
      StringEquals: Join
      Variable: '{{DomainJoinActivity}}'
    - NextStep: unjoinDomain
      StringEquals: Unjoin
      Variable: '{{DomainJoinActivity}}'
  isCritical: 'true'
  isEnd: false
- name: joinDomain
  action: aws:runCommand
  description: Execute PowerShell locally on EC2 instance to join the AD domain.
  inputs:
    Parameters:
      commands: |-
        If ((Get-CimInstance -ClassName 'Win32_ComputerSystem' -ErrorAction SilentlyContinue | Select-Object -ExpandProperty 'PartOfDomain') -eq $false) {
            Try {
                $jsonSecretValue = (Get-SECSecretValue -SecretId arn:aws:secretsmanager:us-east-1:497488328043:secret:SecretKeyADPasswordResource-8c8mC8elYy7i-h8xzcK).SecretString | ConvertFrom-Json
                $domainName = $jsonSecretValue.domainName
                $domainJoinUserName = $jsonSecretValue.domainJoinUserName
                $domainJoinPassword = $jsonSecretValue.domainJoinPassword | ConvertTo-SecureString -AsPlainText -Force

                # Check if S3 config parameters are provided
                $configS3Bucket = "{{ConfigS3Bucket}}"
                $buPrefix = "{{BUPrefix}}"
                $environment = "{{Environment}}"
                $serverType = "{{ServerType}}"

                if ($configS3Bucket -and $buPrefix -and $environment -and $configS3Bucket -ne "" -and $buPrefix -ne "" -and $environment -ne "") {
                    # Construct S3 key based on BU prefix and environment: BUPrefix_ENV.json
                    $configS3Key = "${buPrefix}_${environment}.json"
                    Write-Output "Reading target OU configuration from S3: s3://$configS3Bucket/$configS3Key"
                    Try {
                        $s3ConfigContent = Read-S3Object -BucketName $configS3Bucket -Key $configS3Key | ConvertFrom-Json

                        # Check if ServerType is specified and exists in serverOUs
                        if ($serverType -and $serverType -ne "" -and $s3ConfigContent.serverOUs -and $s3ConfigContent.serverOUs.PSObject.Properties.Name -contains $serverType) {
                            $targetOU = $s3ConfigContent.serverOUs.$serverType
                            Write-Output "Target OU from S3 config ($configS3Key) for server type '$serverType': $targetOU"
                        } elseif ($s3ConfigContent.basePath) {
                            # Fall back to basePath if serverType not found or not specified
                            $targetOU = $s3ConfigContent.basePath
                            Write-Output "Target OU from S3 config ($configS3Key) using basePath (ServerType '$serverType' not found or not specified): $targetOU"
                        } else {
                            # Fall back to legacy targetOU field if it exists
                            $targetOU = $s3ConfigContent.targetOU
                            Write-Output "Target OU from S3 config ($configS3Key) using legacy targetOU field: $targetOU"
                        }

                        if (-not $targetOU) {
                            throw "No valid target OU found in S3 config"
                        }
                    } Catch [System.Exception] {
                        Write-Output "Failed to read or parse S3 config $configS3Key, falling back to default from Secrets Manager: $_"
                        $targetOU = $jsonSecretValue.defaultTargetOU
                    }
                } else {
                    Write-Output "S3 config parameters not fully specified (Bucket: '$configS3Bucket', BU: '$buPrefix', Env: '$environment'), using default target OU from Secrets Manager"
                    $targetOU = $jsonSecretValue.defaultTargetOU
                }
            } Catch [System.Exception] {
                Write-Output " Failed to get SSM Parameter(s) $_"
            }
            $domainCredential = New-Object System.Management.Automation.PSCredential($domainJoinUserName, $domainJoinPassword)

            Try {
                Write-Output "Attempting to join $env:COMPUTERNAME to Active Directory domain: $domainName and moving $env:COMPUTERNAME to the following OU: $targetOU."
                Add-Computer -ComputerName $env:COMPUTERNAME -DomainName $domainName -Credential $domainCredential -OUPath $targetOU -Options AccountCreate -Comment "{{InstanceId}}" -Restart:$false -ErrorAction Stop
            } Catch [System.Exception] {
                Write-Output "Failed to add computer to the domain $_"
                Exit 1
            }
        } Else {
            Write-Output "$env:COMPUTERNAME is already part of the Active Directory domain $domainName."
            Exit 0
        }
    InstanceIds:
    - '{{InstanceId}}'
    DocumentName: AWS-RunPowerShellScript
  timeoutSeconds: 600
  nextStep: joinADEC2Tag
  isEnd: false
  onFailure: step:failADEC2Tag
- name: joinADEC2Tag
  action: aws:createTags
  description: Add the ADJoined EC2 tag to reflect joining to AD domain.
  inputs:
    ResourceIds:
    - '{{InstanceId}}'
    ResourceType: EC2
    Tags:
    - Value: Join-complete
      Key: ADJoined
  isEnd: false
  nextStep: rebootServer
- name: unjoinDomain
  action: aws:runCommand
  description: Execute PowerShell locally on EC2 instance to unjoin from the AD domain.
  inputs:
    Parameters:
      commands: |-
        If ((Get-CimInstance -ClassName 'Win32_ComputerSystem' -ErrorAction SilentlyContinue | Select-Object -ExpandProperty 'PartOfDomain') -eq $true) {
            Try {
                $jsonSecretValue = (Get-SECSecretValue -SecretId arn:aws:secretsmanager:us-east-1:497488328043:secret:SecretKeyADPasswordResource-8c8mC8elYy7i-h8xzcK).SecretString | ConvertFrom-Json
                $domainName = $jsonSecretValue.domainName
                $domainJoinUserName = $jsonSecretValue.domainJoinUserName
                $domainJoinPassword = $jsonSecretValue.domainJoinPassword | ConvertTo-SecureString -AsPlainText -Force
            } Catch [System.Exception] {
                Write-Output "Failed to get SSM Parameter(s) $_"
            }

            $domainCredential = New-Object System.Management.Automation.PSCredential($domainJoinUserName, $domainJoinPassword)

            If (-not (Get-WindowsFeature -Name 'RSAT-AD-Tools' -ErrorAction SilentlyContinue | Select-Object -ExpandProperty 'Installed')) {
                Write-Output 'Installing RSAT AD Tools to allow domain joining'
                Try {
                    $Null = Add-WindowsFeature -Name 'RSAT-AD-Tools' -ErrorAction Stop
                } Catch [System.Exception] {
                    Write-Output "Failed to install RSAT AD Tools $_"
                    Exit 1
                }    
            }
            
            $getADComputer = (Get-ADComputer -Identity $env:COMPUTERNAME -Credential $domainCredential)
            $distinguishedName = $getADComputer.DistinguishedName

            Try {
                Remove-Computer -ComputerName $env:COMPUTERNAME -UnjoinDomainCredential $domainCredential -Verbose -Force -Restart:$false -ErrorAction Stop
                Remove-ADComputer -Credential $domainCredential -Identity $distinguishedName -Server $domainName -Confirm:$False -Verbose -ErrorAction Stop
            } Catch [System.Exception] {
                Write-Output "Failed to remove $env:COMPUTERNAME from the $domainName domain and in a Windows Workgroup. $_"
                Exit 1
            }  
        } Else {
            Write-Output "$env:COMPUTERNAME is not part of the Active Directory domain $domainName and already part of a Windows Workgroup."
            Exit 0
        }
    InstanceIds:
    - '{{InstanceId}}'
    DocumentName: AWS-RunPowerShellScript
  timeoutSeconds: 600
  nextStep: unjoinADEC2Tag
  isEnd: false
  onFailure: step:failADEC2Tag
- name: unjoinADEC2Tag
  action: aws:createTags
  description: Update the ADJoined EC2 tag to reflect removal from AD domain.
  inputs:
    ResourceIds:
    - '{{InstanceId}}'
    ResourceType: EC2
    Tags:
    - Value: Unjoin-complete
      Key: ADJoined
  timeoutSeconds: 30
  isEnd: false
  nextStep: stopServer
- name: failADEC2Tag
  action: aws:createTags
  description: Update the ADJoined EC2 tag to reflect a failure in the AD domain join/unjoin process.
  inputs:
    ResourceIds:
    - '{{InstanceId}}'
    ResourceType: EC2
    Tags:
    - Value: Failed
      Key: ADJoined
  timeoutSeconds: 30
  isEnd: false
  nextStep: stopServer
- name: rebootServer
  action: aws:executeAwsApi
  inputs:
    Service: ec2
    Api: RebootInstances
    InstanceIds:
    - '{{InstanceId}}'
  isEnd: true
- name: stopServer
  action: 'aws:executeAwsApi'
  inputs:
    Service: ec2
    Api: StopInstances
    InstanceIds:
    - '{{InstanceId}}'
  isEnd: true
